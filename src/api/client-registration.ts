export const newElement: any = {
//    "client_id": "023d25b1-3b33-4ae9-9271-7f4c78f4cdeb",
   "reg_agent_uid": "e41da580-59ce-47ef-a643-170a80896200",
   "reg_date": "2018-04-29T22:00:00+00:00",
   "reg_pay_by": "self",
   "last_soi_filed": null,
   "notes": null,
   "id": 39,
   "reg_state": "DE",
   "state_entity": "6866008",
//    "reg_agent": {
//        "address": "1209 N Orange St, Wilmington, DE 19801, USA",
//        "uid": "e41da580-59ce-47ef-a643-170a80896200",
//        "title": "National Registered Agents Inc. (NRAI)",
//        "id": 17,
//        "nickname": "NRAI"
//    }
}

export const formConfig: any[] = [
    {
       size: {xs: 2},
       type: "select",
       field: 'reg_state',
       catalogName: 'state'
    },
    {
       size: {xs: 2},
       field: 'state_entity',
       type: 'text'
    },
    {
       size: {xs: 4},
       type: "select-combo",
       field: 'reg_agent',
       catalogName: 'reg_agents'
    },

    {
      size: {xs: 4},
      type: "select-combo",
      field: 'regpayby',
      catalogName: 'regpayby'
   },

   {
      size: {xs: 3},
      type: 'date-picker',
      field: 'last_soi_filed',
   },
   {
      size: {xs: 3},
      type: 'date-picker',
      field: 'reg_date',
   },
   {
      size: {xs: 3},
      type: 'date-picker',
      field: 'last_renewal',
   },
   {
      size: {xs: 3},
      type: 'date-picker',
      field: 'dissolution_date',
      condition: (item: any) => item.primary
   },
   {
      size: {xs: 3},
      type: 'date-picker',
      field: 'Withdrawal date',
      condition: (item: any) => !item.primary
   },
   {  
      size: {xs: 12},
      type: 'text',
      field: 'notes'
   },
]

 export const modelClientRegistration = {newElement, formConfig}