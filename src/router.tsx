import { createBrowserRouter, Navigate } from "react-router";
import App from "./App";
import Layout from "./layouts/dashboard";
import SignIn from "./pages/user/signIn";
import SignUp from "./pages/user/SignUp";
import SignInDemo from "./pages/demo/SignInDemo";
import { page404 } from "./pages/404";
import {PageSettingsManagers} from "./pages/settings/managers";
import { ClientsListNew } from "./pages/clients_new";
import { ClientsItem } from "./pages/clients_new/item";
import PageSettingsCatalog from "./pages/settings/catalog";
import { PageSettingsTableApi } from "./pages/settings/settings_table_api";
import { Service, serviceSchema } from "./components_lib/models/service";
import { RegAgent, regAgentSchema } from "./components_lib/models/reg-agetn";
import { Source, sourceSchema } from "./components_lib/models/source";
import { Person, personSchema } from "./components_lib/models/person";
import { Address, addressSchema } from "./components_lib/models/address";

  const router = createBrowserRouter([
    {
        Component: App,
        children: [
          {
            path: '/',
            Component: Layout,
            children: [
              {
                path: '/',
                element: <Navigate to="/clients" replace />,
                // Component: DashboardPage,
              },
              
              // {
              //   path: '/clients',
              //   Component: ClientsList,
              // },
              // {
              //   path: '/clients/:clientID',
              //   Component: PageClientItem
              // },
              {
                path: '/clients',
                Component: ClientsListNew,
              },
              {
                path: '/clients/:clientId',
                Component: ClientsItem
              },
              {
                path: '/settings/addresses',
                // Component: PageSettingsAddresses
                element: <PageSettingsTableApi<Address> schema={addressSchema} apiUrl='/addresses' />
              },
              {
                path: '/settings/peoples',
                // Component: PageSettingsPersons
                element: <PageSettingsTableApi<Person> schema={personSchema} apiUrl='/client_persons' />
              },
              {
                path: '/settings/managers',
                Component: PageSettingsManagers
              },
              {
                path: '/settings/reg_agents',
                // Component: PageSettingsRegAgents
                element: <PageSettingsTableApi<RegAgent> schema={regAgentSchema} apiUrl='/reg_agents' />
              },
              {
                path: '/settings/services',
                // Component: PageSettingsServices,
                element: <PageSettingsTableApi<Service> schema={serviceSchema} apiUrl='/services' />
              },
              {
                path: '/settings/sources',
                // Component: PageSettingsServices,
                element: <PageSettingsTableApi<Source> schema={sourceSchema} apiUrl='/sources' />
              },
              {
                path: '/settings/catalog',
                Component: PageSettingsCatalog
              }
              // {
              //   path: '/orders',
              //   Component: OrdersPage,
              // },
            ],
          },
          {
            path: '/sign-in',
            Component: SignIn,
          },
          {
            path: '/sign-up',
            Component: SignUp,
          },
          {
            path: '/demo-sign-in',
            Component: SignInDemo,
            // children: [
            //   {
            //     path: '/sign-in',
            //     Component: SignInDemo,
            //   }
            // ]
            
          },
          {
            path: '*',
            Component: page404
          }
        ],
      },
      
  ]);


export default router;