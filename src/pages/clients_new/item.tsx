import React, { Component } from 'react';
import { Alert, Box, Button, ButtonGroup, Grid, ListItemIcon, ListItemText, Menu, MenuItem, Stack, Tab, Tabs, Typography } from '@mui/material';
import { Client } from '@/components_lib/models/client';
import { useNavigate, useParams } from 'react-router-dom';
import { apiClient, PaginatedData } from '@/utils/api';
import { ClientMain } from '@/components_lib/client/main';
import { LinearProgress } from '@mui/material';
import ChevronLeftIcon from '@mui/icons-material/ChevronLeft'
import { ClientRegistration } from '@/components_lib/client/Registration';
import { ClientAddressTab } from '@/components_lib/client/address';

import { ClientBankTab } from '@/components_lib/client/bank';
import { FormBySchema } from '@/components_lib/FormBySchema';
import { clientMainSchema } from '@/components_lib/models/client/main';
import { SchemaData } from '@/components_lib/models/schema';
import { ClientContactsTab } from '@/components_lib/client/contacts';
import { ClientPaymentCardsTab } from '@/components_lib/client/payment_card';
import { ClientPaymentSystemTab } from '@/components_lib/client/payment_services';
import { ClientShareholdersTab } from '@/components_lib/client/shareholders';
import { ClientTaxReportingTab } from '@/components_lib/client/tax_reporting';
import { ClientShareClassesTab } from '@/components_lib/client/share_classes';
import { ClientAuthorizedSignersTab } from '@/components_lib/client/authorized_signers';
import { ClientServicesTab } from '@/components_lib/client/service';
import { ClientTasksTab } from '@/components_lib/client/tasks';
import { ClientTabList } from '@/components_lib/client/TabList';
import { ClientTask, clientTaskSchema } from '@/components_lib/models/client/task';
import { ClientContact, clientContactSchema } from '@/components_lib/models/client/contat';
import { ClientFileTab } from '@/components_lib/client/files';
import { StatusIcon } from '@/utils/utils';
import ReplayIcon from '@mui/icons-material/Replay';
import { ClientSummaryTab } from '@/components_lib/client/Summary';
import { ClientFinancesTab } from '@/components_lib/client/Finances';

interface Tab {
  
    name: string,
    display_name: string,
    permissions: string[]
    //   "read",
    //   "write",
    //   "delete"
    // ]
}

interface ValidateProps {
  valid:boolean,
  error?: string,
  details: any[],
  message: string
}

interface PendingChangesProps<T> {
  created_at?: string;
  date?: string;
  description?: string;
  extractor_id?: number;
  files?: any[];
  id: number
  note?: string;
  output?: T
  owner_id?: number;
  rating?: number | null;
  status?: {status: string, message: string | null}
  timeline_id?: number; 
  title? : string;
  updated_at?: string;
  workspace_id?: number
}
interface TabPanelProps {
  children?: React.ReactNode;
  dir?: string;
  index: string | number;
  value: string | number;
}

const newTabs: any[] =[
  {name: 'summary', display_name: 'Summary (Legal info)'},
  {name: 'finances', display_name: 'Finances'},
  {name: 'files', display_name: 'Files'},
  {display_name : "Services", name: "services"},
  {display_name: "Tasks", name: "tasks"},
  {display_name: "Tax Reporting", name: "tax_reporting"}
]


function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      <Box sx={{ p: 3 }}>{children}</Box>
      {/* {value === index && <Box sx={{ p: 3 }}>{children}</Box>} */}
    </div>
  );
  }
  

function a11yProps(name: string) {
    return {
      id: `client-tab-${name}`,
      'aria-controls': `clien-tabpanel-${name}`,
      value: name
    };
}

// const componentMaping: any = {
//   main: {component: ClientMain} ,
//   registrations: {component: ClientRegistration},
//   addresses: {component: ClientAddressTab, },
//   authorized_signers: {component: ClientAuthorizedSignersTab},
//   contacts: {component: ClientContactsTab},
//   payment_cards: {component: ClientPaymentCardsTab},
//   payment_services: {component: ClientPaymentSystemTab},
//   shareholders: {component: ClientShareholdersTab},
//   tax_reporting: {component: ClientTaxReportingTab},
//   share_classes: {component: ClientShareClassesTab},
//   services: {component: ClientServicesTab},
//   // tasks: {component: ClientTasksTab},
//   tasks: {component: ClientTabList<Client, ClientTask>,
//     props: {
//       props_name:'tasks'
//     }
//   },
//   bank_accounts: {
//       component: ClientBankTab, 
//       props: {
//           // itemModel: 'ClientBankAccount', 
//           // itemSchema: clientBankAccountSchema
//         }
//       }
// }

export const ClientsItem: React.FC = () => {
  // const [schema, setSchema] = React.useState<SchemaData>(clientListSchema)
  const [error, setError] = React.useState<any| null>(null)
  const [client, setClient] = React.useState<Client | null>(null)
  const [newClient, setNewClient] = React.useState<Client | null>(null)
  const [clientToSave, setClientToSave] = React.useState<Client | null>(null)

  const [loading, setLoading] = React.useState(true)
  const [tabs, setTabs] = React.useState<Tab[]>([])
  const [openTab, setOpenTab] = React.useState<string |number>('');
  const [newOpenTab, setNewOpenTab] = React.useState<string |number>('summary');
  const [sync, setSync] = React.useState<boolean>(false);
  const [validItem, setValidItem] = React.useState<ValidateProps | null>(null);
  const [pendingChanges, setPendingChanges ] = React.useState<PaginatedData<PendingChangesProps<Client>> | null>(null);
  const parentPath = '/clients'

  const hasUpdate = React.useRef(false);
  
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);
  const handleClickValidate = (event: React.MouseEvent<HTMLButtonElement>) => { setAnchorEl(event.currentTarget); };
  const handleCloseValidate = () => { setAnchorEl(null); };


  const [anchorElFC, setAnchorElFC] = React.useState<null | HTMLElement>(null);
  const openFC = Boolean(anchorElFC);
  const handleClickValidateFC = (event: React.MouseEvent<HTMLButtonElement>) => { setAnchorElFC(event.currentTarget); };
  const handleCloseValidateFC = () => { setAnchorElFC(null); };

  const setFCData = (fc: PendingChangesProps<Client>) => {
    if(fc.output) {
      let nc = {...newClient, ...fc.output, internal_data_source: {type: 'pending_change', id: fc.id+''} }
      // setNewClient( nc ),
      hasUpdate.current = true
      saveClientToSave(nc).then(() => {
        getPendingChanges(nc);
      }) 

    } 
    setAnchorElFC(null)
  }


  const params = useParams();
  const navigate = useNavigate();
  
  const clientId = params.clientId;

  const getItem = async () => {
    try{
      setError(null)
      setLoading(true)
      setClient( clientId === 'new' ? {} as Client : await apiClient.get<Client>(`/clients/${clientId}/approved_data`))
      const newClient = clientId === 'new' ? {} as Client : await apiClient.get<Client>(`/clients/${clientId}`)
      setNewClient( newClient )   
      getPendingChanges(newClient)
         
      if(newClient) validateItem(newClient)
      // getTabs()
    } catch(err) {
      debugger
      setError(err)
    }
    setLoading(false)
  }

  const getPendingChanges = async (item:Client | null) => {
    if(!item) return
    try {
      setPendingChanges(await apiClient.get<PaginatedData<PendingChangesProps<Client>>>(`/clients/${item.id}/pending_changes`))
    } catch(err) {
      debugger
    }    
  }

  const validateItem = async (item?: Client) => {
    if(!item) return
    try {
      setValidItem(await apiClient.post<ValidateProps>(`/clients/validate`, item))
    } catch(err) {
      debugger
    }
  }

  // const getTabs = async () => {
  //   const tabs = await apiClient.get<Tab[]>(`/clients/tabs_available`)
  //   setTabs( tabs )
  //   setOpenTab(tabs[0].name)
  // }

  const saveClient = async(data?: Client) => {
    if(!hasUpdate.current) return
    if(!data && newClient) data = newClient;
    let res: Client;
    try{
      setSync(true)
      res = await apiClient.save<Client>(`/clients`, data )
      debugger
      validateItem(data);
      hasUpdate.current = false
      setNewClient(res)
      setError(null)
      if(res.id && !data?.id) {
        navigate(`${parentPath}/${res.id}`, { replace: true })
      }
    } catch (error) {
      setError(error)
    }
    setSync(false)
  }

  const saveClientToSave = async(data: Client, cbSetData = (data: any) => {}) => {
    if(!hasUpdate.current) return
    let res: Client;
    try{
      setSync(true)
      hasUpdate.current = false
      return apiClient.saveSync<Client>(`/clients`, data )
        .then( (r) => {
          if(!hasUpdate.current) {
            validateItem(data);
            hasUpdate.current = false
            cbSetData(data)
          }
        })
        .catch ((err) => {
          validateItem(data);
          setError(err.response.data)
        })
        .finally( () => {
          setSync(false)      
        })
    } catch (error) {
      debugger
    }
  }

  React.useEffect(() => {
    const timeoutId = setTimeout(() => {
      if(!loading && newClient?.id)  saveClientToSave(newClient, setNewClient) // saveClient()
    }, 500);
    return () => clearTimeout(timeoutId);
  }, [newClient, 500]);

  React.useEffect(() => {
    const timeoutId = setTimeout(() => {
      if(!loading && clientToSave?.id) saveClientToSave(clientToSave, setNewClient)
    }, 500);
    return () => clearTimeout(timeoutId);
  }, [clientToSave, 500]);

  const onChangeClient = React.useCallback((data: any) => { 
    // debugger
    hasUpdate.current = true
    console.log('onChangeClient', data)
    setNewClient({...data})
  }, [setNewClient])

  const onChangeClientList = React.useCallback((data: any) => { 
    hasUpdate.current = true
    setClientToSave({...data})
  }, [setClientToSave])

  const handleChangeTabs = (event: React.SyntheticEvent, newValue: number) => {
    setOpenTab(newValue);
  };

  const handleChangeNewTabs = (event: React.SyntheticEvent, newValue: number) => {
    setNewOpenTab(newValue);
  };

  const action = ( event: any)  => {
    switch(event.action) {
        case 'goToParent':
            // history.push(".");
            navigate(parentPath, { replace: true });
    }
  }

  const actionSave = () => {
    debugger
  }

  const actionApprove = async () => {
    setLoading(true)
    setError(null)
    try{
      await apiClient.put<Client>(`/clients/${newClient?.id}/approve`, null)
      await getItem()
    } catch(err) {
      setError(err)
    }
    
    setLoading(false)
  }

  React.useEffect(() => {
    getItem()
  }, [])

  const componentMaping: any = {
    main: {component: ClientMain} ,
    registrations: {component: ClientRegistration},
    addresses: {component: ClientAddressTab, },
    authorized_signers: {component: ClientAuthorizedSignersTab},
    contacts: {component: ClientContactsTab},
    // contacts: {component: ClientTabList<Client, ClientContact>,
    //   props: {
    //     param_name:'contacts',
    //     // onChange : onChangeClientList,
    //     itemSchema: clientContactSchema
    //   }},
    payment_cards: {component: ClientPaymentCardsTab},
    payment_services: {component: ClientPaymentSystemTab},
    shareholders: {component: ClientShareholdersTab},
    tax_reporting: {component: ClientTaxReportingTab},
    share_classes: {component: ClientShareClassesTab},
    services: {component: ClientServicesTab},
    tasks: {component: ClientTasksTab},
    
    // tasks: {component: ClientTabList<Client, ClientTask>,
    //   props: {
    //     param_name:'tasks',
    //     onChange : onChangeClientList,
    //     itemSchema: clientTaskSchema
    //   }
    // },
    files: {component: ClientFileTab},
    bank_accounts: {
        component: ClientBankTab, 
        props: {
            // itemModel: 'ClientBankAccount', 
            // itemSchema: clientBankAccountSchema
          }
        },

    summary: {component : ClientSummaryTab},
    finances: {component: ClientFinancesTab}
  }

  const tabContent = (tab: Tab, props: any) => {
    var Component = componentMaping[tab.name]?.component
    var tabProps = componentMaping[tab.name]?.props
    return Component 
            ? <Component {...tabProps} {...props}  />
            : tab.display_name
  }


  return (
    <Box>
      <Stack
      mb={2}
        direction="row"
        spacing={2}
        sx={{
          justifyContent: "space-between",
          alignItems: "center",
        }
      }>
        <ButtonGroup
            variant="text"
            aria-label="button group">
            <Button  color="info" onClick={() => { action({action: 'goToParent'})}}>
                <ChevronLeftIcon /> Back
            </Button>
            {/* <Button color="success" onClick={actionSave}>
                Save
            </Button> */}

            {pendingChanges?.count && (
              <>
                <Button
                  id="file-change-button"
                  aria-controls={openFC ? 'file-change-menu' : undefined}
                  aria-haspopup="true"
                  aria-expanded={openFC ? 'true' : undefined}
                  onClick={handleClickValidateFC}
                  color='info'
                >File changes</Button>
                <Menu
                    id="file-change-menu"
                    anchorEl={anchorElFC}
                    open={openFC}
                    onClose={handleCloseValidateFC}
                    MenuListProps={{
                      'aria-labelledby': 'lock-button',
                      role: 'listbox',
                    }}
                  >
                    {pendingChanges.items.map( (itemFC: any) => (
                      
                      <MenuItem key={itemFC.id}>
                        
                        <ListItemIcon>
                          <StatusIcon status={itemFC.status}></StatusIcon>
                        </ListItemIcon>
                        <ListItemText
                          primary={itemFC.title}
                          secondary={itemFC.description}
                          onClick={() => {setFCData(itemFC)}}
                        ></ListItemText>
                      </MenuItem>
                    ))}
                    <MenuItem >
                        <ListItemIcon>
                          <ReplayIcon fontSize="small"></ReplayIcon>
                        </ListItemIcon>
                        <ListItemText
                          primary="Update list"
                          onClick={() => {getPendingChanges(newClient)}}
                        ></ListItemText>
                      </MenuItem>
                </Menu>      
              </>

            )}

            {validItem && validItem.valid && newClient?.internal_draft_flag && <Button color="success" onClick={actionApprove}>
                Approve
            </Button>}
            {validItem && !validItem.valid  &&
              <Button
                  id="valid-button"
                  aria-controls={open ? 'valid-menu' : undefined}
                  aria-haspopup="true"
                  aria-expanded={open ? 'true' : undefined}
                  onClick={handleClickValidate}
                  color='warning'
                >
                  Validation Error
              </Button>
            }
        </ButtonGroup>
        

        <Menu
          id="valid-menu"
          anchorEl={anchorEl}
          open={open}
          onClose={handleCloseValidate}
          MenuListProps={{
            'aria-labelledby': 'lock-button',
            role: 'listbox',
          }}
          >
          <Box sx={{ m: 2 }}>
            <Alert severity="warning"><pre>{validItem?.error}</pre></Alert>
              <pre>{validItem?.message}</pre>
          </Box>
        </Menu>      

        {sync && <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Typography 
              variant="caption"
              component="div"
              sx={{ color: 'text.secondary' }}
            >
              Syncing
            </Typography>
            <Box sx={{ width: '20px', marginLeft: '1em' }}>
              <LinearProgress />
            </Box>
          </Box>
        }
      </Stack>
      {error && 
        <Alert
          severity="error"
          sx={{ mb: 2 }}
        >
          Client data is loading
          <p>{JSON.stringify(error)}</p>
        </Alert>
      }

      {
        newClient?.id 
        ? loading 
          ? <LinearProgress /> 
          : <>
            <Box>
              <ClientMain
                data={newClient || {}as Client}
                baseData={client}
                onChange={onChangeClient}
              />
            </Box>


            <>
              <Box sx={{ borderBottom: 1, borderColor: 'divider', marginBottom:3, bgcolor: 'primary.main', color: 'primary.contrastText',}}>
                <Tabs 
                  value={newOpenTab} 
                  onChange={handleChangeNewTabs} 
                  aria-label="basic tabs example"
                  variant="scrollable"
                  indicatorColor="secondary"
                  textColor="inherit"
                  scrollButtons="auto"  
                  >
                  {newTabs.map( (tab: Tab) => (<Tab key={tab.name} label={tab.display_name} {...a11yProps(tab.name)} />))}
                </Tabs>
              </Box>
              <Box sx={{ borderLeft: 3, borderColor: 'divider' }}>
                {newTabs.map((tab: Tab)=>(
                  <TabPanel key={tab.name} value={newOpenTab} index={tab.name} >
                    { tab.name == newOpenTab &&
                      tabContent(tab, {data: newClient || {} as Client, onChange: onChangeClient, onChangeList: onChangeClientList, baseData: client})
                    }
                  </TabPanel>))
                }
              </Box>
            </>


            {tabs && openTab && (
              <>
                <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                  <Tabs 
                    value={openTab} 
                    onChange={handleChangeTabs} 
                    aria-label="basic tabs example"
                    variant="scrollable"
                    scrollButtons="auto"  
                    >
                    {tabs.map( (tab: Tab) => (<Tab key={tab.name} label={tab.display_name} {...a11yProps(tab.name)} />))}
                  </Tabs>
                </Box>
                <Box>
                  {tabs.map((tab: Tab)=>(
                    <TabPanel key={tab.name} value={openTab} index={tab.name} >
                      { tab.name == openTab &&
                        tabContent(tab, {data: newClient || {} as Client, onChange: onChangeClient, onChangeList: onChangeClientList, baseData: client})
                      }
                    </TabPanel>))
                  }
                </Box>
              </>
            )}
            
          {/* <Grid container spacing={3}>
            <Grid size={6}>
              <Typography >data</Typography>
              <pre>{JSON.stringify(client, null, 2)}</pre>    
            </Grid>
            <Grid size={6}>
            <Typography >new data</Typography>
              <pre>{JSON.stringify(newClient, null, 2)}</pre>    
            </Grid>
          </Grid> */}
          </>
          :
          <>
            <FormBySchema<Client>
                    schema={ { name: clientMainSchema.name} as SchemaData}
                    initialValues={newClient || {} as Client}
                    onSubmit={saveClient}
                    onChange={onChangeClient}
                    // actions={false}
                />
          </>
        
      }
    
    </Box>
  );
}; 