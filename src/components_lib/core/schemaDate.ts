// export interface SchemaDataValue {
//   /** Тип поля */
//   type: 'text' | 'number' | 'date' | 'select' | 'textarea' | 'autocomplete';
//   /** Метка поля */
//   label: string;
//   /** Обязательное ли поле */
//   required?: boolean;
//   /** Значение по умолчанию */
//   defaultValue?: any;
//   /** Опции для select */
//   options?: (string | number | boolean | { value: string | number; label: string })[];
//   /** Валидация */
//   validation?: {
//     pattern?: RegExp;
//     message?: string;
//   };
//   /** Размер поля в сетке (1-12) */
//   gridSize?: number;
//   /** Множественный выбор для select */
//   multiple?: boolean;
//   /** Максимальная длина для текстовых полей */
//   maxLength?: number;
//   /** Минимальное значение для числовых полей */
//   min?: number;
//   /** Максимальное значение для числовых полей */
//   max?: number;
//   /** Настройки для автоподстановки */
//   autocomplete?: {
//     /** URL для загрузки данных */
//     apiUrl: string;
//     /** Ключ для отображения в выпадающем списке */
//     labelKey: string;
//     /** Ключ для значения */
//     valueKey: string;
//     /** Задержка перед запросом (мс) */
//     debounce?: number;
//     /** Минимальное количество символов для поиска */
//     minChars?: number;
//     /** Параметры запроса */
//     params?: Record<string, any>;
//     /** Сохранять полный объект или только значение */
//     saveFullObject?: boolean;
//     /** Функция для форматирования отображения объекта */
//     formatOptionLabel?: (option: any) => string;
//   };
//   /** Функция для получения значения поля */
//   getValue?: (values: Record<string, any>, key?: string) => any;
//   /** Функция для установки значения поля */
//   setValue?: (values: Record<string, any>, value: any) => Record<string, any>;
//   /** Можно ли редактировать поле в форме (по умолчанию true) */
//   editable?: boolean;
//   /** Отображать ли поле в таблице (по умолчанию true) */
//   visible?: boolean;
//   /** Является ли поле многострочным текстом */
//   multiline?: boolean;
// }

// export interface SchemaData {
//   [key: string]: SchemaDataValue;
// } 