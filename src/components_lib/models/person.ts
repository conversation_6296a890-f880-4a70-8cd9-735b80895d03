import { SchemaData } from './schema';

export interface Person {
  id: string;
  firstname?: string;
  lastname?: string;
  full_title?: string;
  email?: string;
  phone?: string;
  address?: string;
  citizenship?: string;
  companies?: string;
  pcm?: string;
  created_at?: string | null;
  updated_at?: string | null;
}

export const personSchema: SchemaData = {
  firstname: {
    type: 'text',
    label: 'First Name',
    required: true,
    grid: {
      size: { xs: 6 }
    },
    validation: {
      pattern: /^[а-яА-Яa-zA-Z\s-]+$/,
      message: 'First name can only contain letters, spaces, and hyphens',
    }
  },
  lastname: {
    type: 'text',
    label: 'Last Name',
    required: true,
    grid: {
      size: { xs: 6 }
    },
    validation: {
      pattern: /^[а-яА-Яa-zA-Z\s-]+$/,
      message: 'Last name can only contain letters, spaces, and hyphens',
    }
  },
  email: {
    type: 'text',
    label: 'Email',
    grid: {
      size: { xs: 6 }
    },
    validation: {
      pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
      message: 'Please enter a valid email address',
    }
  },
  phone: {
    type: 'text',
    label: 'Phone',
    grid: {
      size: { xs: 6 }
    },
    validation: {
      pattern: /^[\d\s()+-]+$/,
      message: 'Please enter a valid phone number',
    }
  },
  address: {
    type: 'text',
    label: 'Address',
    multiline: true,
    grid: {
      size: { xs: 12 }
    }
  },
  citizenship: {
    type: 'text',
    label: 'Citizenship',
    grid: {
      size: { xs: 6 }
    }
  },
  companies: {
    type: 'text',
    label: 'Companies',
    multiline: true,
    grid: {
      size: { xs: 6 }
    },
    // helperText: 'Enter comma-separated list of companies'
  },
  pcm: {
    type: 'text',
    label: 'PCM',
    grid: {
      size: { xs: 12 }
    }
  }
};
