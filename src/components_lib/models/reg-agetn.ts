import { SchemaData } from './schema';

export interface RegAgent {
    id?: string;
    uid?: string;
    created_at?: string | null;
    updated_at?: string | null;

    nickname: string | null;
    address: string | null;
    title:  string
  };


export const regAgentSchema: SchemaData = {

  title: {
    type: 'text',
    label: 'title',
    
  },
  nickname: {
    type: 'text',
    label: 'nickname',
  },
  address: {
    type: 'text',
    label: 'address',
    
  },
  
  
}; 