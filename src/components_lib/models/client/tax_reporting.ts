import { Address } from "../address";
import { Person } from "../person";
import { SchemaData } from '../schema';

export interface ClientTaxReporting {
    id?: string;
    uid?: string;
    
    year: string;
    reporting_1099: string;
    tax_return_by: string;
    note: string | null;
    files: string | null;

    created_at?: string | null;
    updated_at?: string | null;
}

export const clientTaxReportingSchema: SchemaData = {
  year: {
    type: 'text',
    label: 'Year',
    grid: { size: { xs: 2 } }
  },

  reporting_1099: {
    type: 'select',
    label: 'Reporting 1099',
    catalog: 'yes_no',
    grid: { size: { xs: 2 } }
  },

  tax_return_by: {
    type: 'select',
    label: 'Tax return by',
    catalog: 'yes_no',
    grid: { size: { xs: 2 } }
  },
  
  note: {
    type: 'text',
    label: 'Note',
    grid: {
      size: { xs: 3 }
    }
  },
  files: {
    type: 'text',
    label: 'files',
    grid: {
      size: { xs: 3 }
    }
  }
};