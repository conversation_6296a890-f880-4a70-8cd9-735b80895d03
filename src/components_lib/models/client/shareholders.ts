import { Address } from "../address";
import { Person } from "../person";
import { SchemaData } from '../schema';

// export interface Contact {
//   id: number;
//   client_id: string;
//   client_person_id: string;
//   position: string;
//   email: string;
//   phone: string;
//   pcm: string;
//   note: string;
// }



export interface ClientShareholders {
    id?: string;
    uid?: string;
    
    position: string;
    ownership: string;
    note: string;

    created_at?: string | null;
    updated_at?: string | null;
}

export const clientShareholdersSchema: SchemaData = {
  position: {
    type: 'select',
    label: 'Position',
    catalog: 'position',
    grid: {
      size: { xs: 3 }
    }
  },
  person: {
    type: 'autocomplete',
    label: 'Person',
    autocomplete: {
      apiUrl: '/client_persons',
      labelKey: 'name',
      valueKey: 'id',
      formatOptionLabel: (option: Address) =>  option && option.full_title ? option.full_title : '-',
      saveFullObject: true
    },
    grid: {
      size: { xs: 3 }
    }
  },
  
  ownership: {
    type: 'text',
    label: 'Ownership',
    grid: {
      size: { xs: 2 }
    }
  },
  note: {
    type: 'text',
    label: 'Note',
    multiline: true,
    grid: {
      size: { xs: 4}
    }
  }
};