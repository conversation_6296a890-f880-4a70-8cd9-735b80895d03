import { Address } from "../address";
import { Person } from "../person";
import { SchemaData } from '../schema';

export interface ClientPaymentServices {
    id?: string;
    uid?: string;
    
    payment_system: string;
    date_opened: string;
    opened_by: string;
    email_connected: string;
    responsible_person: string;
    note: string | null

    created_at?: string | null;
    updated_at?: string | null;
}

export const clientPaymentServicesSchema: SchemaData = {
  payment_system: {
    type: 'select',
    label: 'payment_system',
    catalog: 'payment_system',
    grid: {
      size: { xs: 3 }
    }
  },
  date_opened: {
    type: 'date',
    label: 'Date Opened',
    grid: {
      size: { xs: 3 }
    }
  },
  
  email_connected: {
    type: 'text',
    label: 'email_connected',
    grid: {
      size: { xs: 3 }
    }
  },
  responsible_person: {
    type: 'text',
    label: 'Responsible Person',
    grid: {
      size: { xs: 3 }
    }
  },
  note: {
    type: 'text',
    label: 'Note',
    multiline: true,
    grid: {
      size: { xs: 12}
    }
  }
};