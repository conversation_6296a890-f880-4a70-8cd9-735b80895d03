import { SchemaData } from "../schema";

export const clientMainSchema: SchemaData = {
    name: {
      type: 'text',
      label: 'Company Name',
      required: true,
      validation: {
        pattern: /^[a-zA-Z0-9\s.,&-]+$/,
        message: 'Company name can contain letters, numbers, spaces, and basic punctuation',
      },
      grid: {size: 4}
    },
    status: {
      type: 'select',
      label: 'Status',
    //   required: true,
    //   options: ['hourly', 'active', 'inactive', 'suspended'],
      catalog: 'status',
      grid: {size: 2}
    },
    active_since: {
      type: 'date',
      label: 'Active Since',
      grid: {size: 2}
    //   required: true,
    },
    source: {
      type: 'autocomplete',
        label: 'Source',
        autocomplete: {
          apiUrl: '/sources',
          labelKey: 'title',
          valueKey: 'title',
          formatOptionLabel: (option: any) =>  typeof option === 'object' ? option.title : option ,
          saveFullObject: true,
          loadOptionsOnMount: true,
        },
      grid: {size: 2}
    },
    manager: {
        type: 'autocomplete',
        label: 'Manager',
        autocomplete: {
          apiUrl: '/managers',
          labelKey: 'name',
          valueKey: 'id',
          formatOptionLabel: (option: any) =>  option.title ? option.title : '-',
          saveFullObject: true,
          loadOptionsOnMount: true,
        },
        // editable:true,
        grid: {size: 2}
    },
    description: {
      type: 'text',
      label: 'Description',
    //   multiline: true,
      grid: {size: 6}
    },
    notes_main: {
      type: 'text',
      label: 'Notes',
    //   multiline: true,
      grid: {size: 6}
    },
  }