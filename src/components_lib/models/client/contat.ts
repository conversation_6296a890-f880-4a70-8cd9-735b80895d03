import { Address } from "../address";
import { Person } from "../person";
import { SchemaData } from '../schema';
export interface ClientContact {
    id?: string;
    uid?: string;
    
    position: string;
    email: string;
    phone: string;
    pcm: string;
    note: string;
    person: Person | null

    created_at?: string | null;
    updated_at?: string | null;
}

export const clientContactSchema: SchemaData = {
  position: {
    type: 'select',
    label: 'Position',
    catalog: 'position',
    grid: {
      size: { xs: 6 }
    }
  },
  person: {
    type: 'autocomplete',
    label: 'Person',
    autocomplete: {
      apiUrl: '/client_persons',
      labelKey: 'name',
      valueKey: 'id',
      formatOptionLabel: (option: Address) =>  option && option.full_title ? option.full_title : '-',
      saveFullObject: true
    },
    grid: {
      size: { xs: 6 }
    }
  },
  
  email: {
    type: 'text',
    label: 'Email',
    grid: {
      size: { xs: 4 }
    }
  },
  phone: {
    type: 'text',
    label: 'Phone',
    grid: {
      size: { xs: 4 }
    }
  },
  pcm: {
    type: 'text',
    label: 'PCM',
    grid: {
      size: { xs: 4 }
    }
  },
  note: {
    type: 'text',
    label: 'Note',
    multiline: true,
    grid: {
      size: { xs: 12}
    }
  }
};