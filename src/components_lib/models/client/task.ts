import { ListItem } from '@/components_lib/ListFormBySchema';
import { Manager } from '../manager';
import { SchemaData } from '../schema';


export interface ClientTask extends ListItem{
    id?: string;
    uid?: string;
    created_at?: string | null;
    updated_at?: string | null;

    description: string | null;
    date: string | null;
    name: string;
    due_date: string | null;
    status: string | null;
    client_service_id: number | null;
    tax_report_id: number | null;
    manager_id: string | null;
}

export const clientTaskSchema: SchemaData = {

  name:{
    type: 'text',
    label: 'Name',
    grid: { size: { xs: 4 } },
    required: true
  },
  date: {
    type: 'date',
    label: 'Date',
    grid: { size: { xs: 4 } },

  },
  due_date: {
    type: 'date',
    label: 'Date',
    grid: { size: { xs: 4 } },
  },

    manager: {
    type: 'autocomplete',
    label: 'Manager',
    autocomplete: {
      apiUrl: '/managers',
      labelKey: 'name',
      valueKey: 'id',
      formatOptionLabel: (option: Manager) =>  option.user ? option.user?.name : '-',
      saveFullObject: true,
      loadOptionsOnMount: true,
    },
    grid: {size: 4}
  },


  status: {
    type: 'select',
    label: 'Status',
    catalog: 'task_status',
    grid: { size: { xs: 4 } }
  },

  description: {
    type: 'text',
    label: 'Description',
    multiline: true,
  },
  
  
};