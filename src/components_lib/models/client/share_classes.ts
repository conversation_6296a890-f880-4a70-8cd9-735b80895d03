import { Address } from "../address";
import { Person } from "../person";
import { SchemaData } from '../schema';
export interface ClientShareClasses {
    id?: string;
    uid?: string;
    
    stock_authorized: number;
    stock_issued: number;
    shares_authorized_preferred: string;
    shares_issued_preferred: number;
    notes: string | null;

    created_at?: string | null;
    updated_at?: string | null;
}

export const clientShareClassesSchema: SchemaData = {

  
  stock_authorized: {
    type: 'number',
    label: 'Stock Authorized',
    grid: {
      size: { xs: 2 }
    }
  },
  stock_issued: {
    type: 'number',
    label: 'Stock Issued',
    grid: {
      size: { xs: 2 }
    }
  },
  shares_authorized_preferred: {
    type: 'text',
    label: 'Shares Authorized Preferred',
    grid: {
      size: { xs: 2 }
    }
  },

  shares_issued_preferred: {
    type: 'number',
    label: 'Shares Issued Preferred',
    grid: {
      size: { xs: 2 }
    }
  },
  note: {
    type: 'text',
    label: 'Note',
    multiline: true,
    grid: {
      size: { xs: 4}
    }
  }
};