import { SchemaData } from '../schema';



export interface ClientRegistrationMain {
  legal_ent_type?: string;
  ein?: string;
  incorp_by?: string;
  naicscode?: string;
  fedtaxforms?: string;
  statetaxforms?: string;
  notes_registration?: string;
}

export const clientRegistrationMainSchema: SchemaData = {
  legal_ent_type: {
    type: 'select',
    label: 'Legal Entity Type',
    catalog: 'legal_ent_type',
    grid: {
      size: { xs: 2 }
    }
  },
  ein: {
    type: 'text',
    label: 'EIN',
    grid: {
      size: { xs: 2 }
    }
  },
  incorp_by: {
    type: 'select',
    label: 'Incorporated By',
    catalog: 'incorp_by',
    grid: {
      size: { xs: 2 }
    }
  },
  naicscode: {
    type: 'text',
    label: 'NAICS Code',
    grid: {
      size: { xs: 2 }
    }
  },
  fedtaxforms: {
    type: 'select',
    label: 'Federal Tax Forms',
    catalog: 'fed_tax_forms',
    grid: {
      size: { xs: 2 }
    }
  },
  statetaxforms: {
    type: 'select',
    label: 'State Tax Forms',
    catalog: 'state',
    grid: {
      size: { xs: 2 }
    }
  },
  notes_registration: {
    type: 'text',
    label: 'Registration Notes',
    multiline: true,
    grid: {
      size: { xs: 12 }
    }
  }
};
