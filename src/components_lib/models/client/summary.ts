import { SchemaData } from "../schema";

export const clientSummarySchema: SchemaData = {
    
    name: {
      type: 'text',
      label: 'Company Name',
      required: true,
      validation: {
        pattern: /^[a-zA-Z0-9\s.,&-]+$/,
        message: 'Company name can contain letters, numbers, spaces, and basic punctuation',
      },
      grid: {size: 4}
    },
    legal_ent_type: {
        type: 'select',
        label: 'Legal Entity Type',
        catalog: 'legal_ent_type',
        grid: {
          size: { xs: 2 }
        }
    },
    ein: {
        type: 'text',
        label: 'EIN',
        grid: {
          size: { xs: 2 }
        }
    },
    active_since: {
        type: 'date',
        label: 'Active Since',
        grid: {size: 2}
    },
    status: {
      type: 'select',
      label: 'Status',
    //   required: true,
    //   options: ['hourly', 'active', 'inactive', 'suspended'],
      catalog: 'status',
      grid: {size: 2}
    },
}

export const clientSummaryAdvancedSchema: SchemaData = {
    incorp_by: {
        type: 'select',
        label: 'Incorporated By',
        catalog: 'incorp_by',
        grid: {size: { xs: 2 }}
    },
    naicscode: {
        type: 'text',
        label: 'NAICS Code',
        grid: {size: { xs: 2 }}
    },
    fedtaxforms: {
        type: 'select',
        label: 'Federal Tax Forms',
        catalog: 'fed_tax_forms',
        grid: { size: { xs: 2 } }
    },
    statetaxforms: {
        type: 'select',
        label: 'State Tax Forms',
        catalog: 'state',
        grid: { size: { xs: 2 } }
    },

    source: {
        type: 'autocomplete',
          label: 'Source',
          autocomplete: {
            apiUrl: '/sources',
            labelKey: 'title',
            valueKey: 'title',
            formatOptionLabel: (option: any) =>  typeof option === 'object' ? option.title : option ,
            saveFullObject: true,
            loadOptionsOnMount: true,
          },
        grid: {size: 2}
      },
      manager: {
          type: 'autocomplete',
          label: 'Manager',
          autocomplete: {
            apiUrl: '/managers',
            labelKey: 'name',
            valueKey: 'id',
            formatOptionLabel: (option: any) =>  option.title ? option.title : '-',
            saveFullObject: true,
            loadOptionsOnMount: true,
          },
          // editable:true,
          grid: {size: 2}
      },
    // notes_registration: {
    //     type: 'text',
    //     label: 'Registration Notes',
    //     multiline: true,
    //     grid: { size: { xs: 12 } }
    // },
    
    description: {
      type: 'text',
      label: 'Description',
    //   multiline: true,
      grid: {size: 6}
    },
    notes_main: {
      type: 'text',
      label: 'Notes',
    //   multiline: true,
      grid: {size: 6}
    },
  }