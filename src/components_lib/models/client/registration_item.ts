import { RegAgent } from '../reg-agetn';
import { SchemaData } from '../schema';


export interface ClientRegistrationItem {
  id?: number|string;
  uid?: number|string;
  reg_state?: string;
  state_entity?: string;
  reg_agent?: string;
  regpayby?: string;
  last_soi_filed?: Date | null;
  reg_date?: Date | null;
  last_renewal?: Date | null;

  dissolution_date?: Date | null;
  
  withdrawal_date?: Date | null;
  notes?: string;
  is_primary?: boolean;
}

export const clientRegistrationItemSchema: SchemaData = {
  reg_state: {
    type: 'select',
    label: 'Registration State',
    catalog: 'state',
    grid: {
      size: { xs:6, sm:4,  md:2 }
    }
  },
  state_entity: {
    type: 'text',
    label: 'State Entity',
    grid: {
      size: { xs:6, sm:4,  md:2 }
    }
  },
  reg_agent: {
    type: 'autocomplete',
    label: 'Registration Agent',
    autocomplete: {
      apiUrl: '/reg_agents',
      labelKey: 'name',
      valueKey: 'id',
      formatOptionLabel: (option: RegAgent) =>  option && option.title ? option.title : '-',
      saveFullObject: true
      
    },
    grid: {
      size: { xs:12, sm: 6, md:4 }
    }
  },

  regpayby: {
    type: 'select',
    label: 'Registration Payment By',
    catalog: 'regpayby',
    // type: 'autocomplete',
    // label: 'Registration Payment By',
    // autocomplete: {
    //   apiUrl: '/api/catalogs/regpayby',
    //   labelKey: 'name',
    //   valueKey: 'id',
    // },
    grid: {
      size: { xs:12, sm: 6, md:4 }
    }
  },
  last_soi_filed: {
    type: 'date',
    label: 'Last SOI Filed',
    grid: {
      size: { xs:6, md:3 }
    }
  },
  reg_date: {
    type: 'date',
    label: 'Registration Date',
    grid: {
      size: { xs:6, md:3 }
    }
  },
  last_renewal: {
    type: 'date',
    label: 'Last Renewal',
    grid: {
      size: { xs:6, md:3 }
    }
  },
  
  withdrawal_date: {
    type: 'date',
    label: 'Withdrawal Date',
    grid: {
      size: { xs:6, md:3 }
    },
    // visible: (values) => values.primary !== true
  },
  notes: {
    type: 'text',
    label: 'Notes',
    multiline: true,
    grid: {
      size: { xs: 12 }
    }
  }
};
