import { Address } from "../address";
import { Person } from "../person";
import { SchemaData } from '../schema';

export interface ClientBankCard {
    id?: string | number;
    uid?: string;

    card_number: string | null;
    last_4_digits: string | null;
    cvv: string | null;
    card_holder_name: string | null;
    valid_through: string | null;
    expired_at: string | null;

}

export const clientBankCardSchema: SchemaData = {

  card_number: {
    type: 'text',
    label: 'Card umber',
    grid: {
      size: { xs: 4 }
    },
    // slotProps: {htmlInput: { 
    //   type:"tel", 
    //   inputmode:"numeric", 
    //   pattern:"[0-9\s]{13,19}",
    //   autocomplete:"cc-number",
    //   maxlength:"19",
    //   placeholder:"xxxx xxxx xxxx xxxx"
    //  } }
  },

  last_4_digits: {
    type: 'text',
    label: 'Last 4 digits',
    grid: {
      size: { xs: 4 }
    },
    disabled: true,
    getValue: (row: any) => row.last_4_digits ? row.last_4_digits : row.card_number?.substring(row.card_number?.length - 4)
  },
  cvv: {
    type: 'text',
    label: 'CVV',
    grid: {
      size: { xs: 4 }
    }
  },
  card_holder_name: {
    type: 'text',
    label: 'Card holder name',
    grid: {
      size: { xs: 4 }
    }
  },

  valid_through: {
    type: 'text',
    label: 'Valid through',
    grid: {
      size: { xs: 4 }
    }
  },

  expired_at: {
    type: 'text',
    label: 'Expired at',
    grid: {
      size: { xs: 4}
    }
  }
};