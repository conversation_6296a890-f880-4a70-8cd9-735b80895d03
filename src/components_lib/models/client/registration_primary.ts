import { RegAgent } from '../reg-agetn';
import { SchemaData } from '../schema';

export interface ClientPrimaryRegistration {

  id : number
  
  reg_state : string | null
  state_entity : null
  
  last_soi_filed : string | null

  deregister_date : string | null
  
  last_renewal_date : string | null
  
  
  
  reg_agent : string | null
  reg_agent_id : string | null
  reg_date : string | null
  reg_pay_by : string | null
  
  notes : string | null

  client_id : string | null
  created_at : string | null
  updated_at : string | null
}



export const clientPrimaryRegistrationSchema: SchemaData = {
  reg_state: { type: 'select', label: 'Registration State', catalog: 'state', grid: { size: { xs:6, sm:4,  md:2 } } },
  state_entity: { type: 'text', label: 'State Entity', grid: { size: { xs:6, sm:4,  md:2 } } },
  reg_agent: {
    type: 'autocomplete',
    label: 'Registration Agent',
    autocomplete: {
      apiUrl: '/reg_agents',
      labelKey: 'name',
      valueKey: 'id',
      formatOptionLabel: (option: RegAgent) =>  option && option.title ? option.title : '-',
      saveFullObject: true
      
    },
    grid: {
      size: { xs:12, sm: 6, md:6 }
    }
  },
  
  reg_pay_by: {
    type: 'select',
    label: 'Registration Payment By',
    catalog: 'regpayby',
    grid: {
      size: { xs:12, sm: 4, md:2 }
    }
  },
  reg_date: {
    type: 'date',
    label: 'Registration Date',
    grid: {
      size: { xs:6, md:3 }
    }
  },
  last_renewal_date: {
    type: 'date',
    label: 'Last renewal date',
    grid: {
      size: { xs:6, md:3 }
    }
  },
  last_soi_filed: {
    type: 'date',
    label: 'Last SOI Filed',
    grid: {
      size: { xs:6, md:3 }
    }
  },

  dissolution_date: {
    type: 'date',
    label: 'Dissolution Date',
    grid: {
      size: { xs:6, md:3 }
    },
  },
  notes: {
    type: 'text',
    label: 'Notes',
    multiline: true,
    grid: {
      size: { xs: 12 }
    }
  }
  
}
