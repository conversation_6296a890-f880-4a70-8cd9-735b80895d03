import { Address } from "../address";
import { SchemaData } from '../schema';

export interface ClientAddress {
    id?: string;
    uid?: string;
    
    address_type?: string;
    address?: Address | null;
    renewal_date?: string | null;
    phone?: string | null;
    paid_by?: string | null;
    note?: string | null;

    created_at?: string | null;
    updated_at?: string | null;
}

export const clientAddressSchema: SchemaData = {
  address_type: {
    type: 'select',
    label: 'Address Type',
    catalog: 'address_type',
    grid: {
      size: { xs: 4 }
    }
  },
  address: {
    type: 'autocomplete',
    label: 'Address',
    autocomplete: {
      apiUrl: '/addresses',
      labelKey: 'name',
      valueKey: 'id',
      // formatOptionLabel: (option: Address) =>  option && option.full_title ? option.full_title : '-',
      formatOptionLabel: (option: Address) =>  option.full_title || option.full_address || '-',
      saveFullObject: true
    },
    grid: {
      size: { xs: 4 }
    }
  },
  paid_by: {
    type: 'select',
    label: 'Paid By',
    catalog: 'address_paid_by',
    grid: {
      size: { xs: 2 }
    }
  },
  renewal_date: {
    type: 'date',
    label: 'Renewal Date',
    grid: {
      size: { xs: 2 }
    }
  },
  phone: {
    type: 'text',
    label: 'Phone',
    grid: {
      size: { xs: 2 }
    }
  },
  note: {
    type: 'text',
    label: 'Note',
    multiline: true,
    grid: {
      size: { xs: 10 }
    }
  }
};