import { Interface } from 'readline';
import { ClientAddress } from './client/address';
import { ClientBankAccount } from './client/bank_account';
import { ClientRegistrationItem } from './client/registration_item';
import { SchemaData } from './schema';
import { ClientContact } from './client/contat';
import { DataItem } from '@/utils/api';
import { ClientAuthorizedSigners } from './client/authorized_signers';
import { ClientPaymentCards } from './client/payment_card';
import { ClientPaymentServices } from './client/payment_services';
import { ClientShareholders } from './client/shareholders';
import { ClientTaxReporting } from './client/tax_reporting';
import { ClientShareClasses } from './client/share_classes';
import { ClientPrimaryRegistration } from './client/registration_primary';

export interface ClientTabProps<T> {
  /** Данные */
  data: T;
  /** Базовые данные для сравнения */
  baseData?: T;
  itemSchema?: SchemaData;  
  itemModel?: Interface;
  itemDefaultValues?: any;
  itemsEl?: string;
  onChange: (data: T) => void;
  onChangeList?: (data: T) => void;
  
}

export interface ClientTabListProps<T> {
  /** Данные */
  data: T;
  /** Базовые данные для сравнения */
  baseData?: T;
  itemSchema: SchemaData;  
  itemModel?: Interface;
  itemDefaultValues?: any;
  itemsEl?: string;
  param_name: string; // keyof T;
  onChange: (data: T) => void;
  onChangeList: (data: T) => void;
  // onChangeList: (data: {list: any[], param_name: string}) => void;
  
}


// export interface AuthorizedSigner {
//   id: number;
//   client_id: string;
//   signer_id: string;
// }


// export interface File {
//   id: string;
//   date: string;
//   name: string;
//   description: string | null;
//   hash: string | null;
//   file_type: string;
//   doc_type: string;
//   client_id: string;
//   client_person_id: string | null;
//   manager_id: string | null;
//   created_at: string;
//   updated_at: string;
// }

// export interface Registration {
//   id: number;
//   client_id: string;
//   reg_agent_id: string;
//   is_primary: boolean | null;
//   reg_date: string | null;
//   deregister_date: string | null;
//   last_renewal_date: string | null;
//   reg_state: string;
//   reg_pay_by: string | null;
//   last_soi_filed: string | null;
//   state_entity: string;
//   notes: string;
// }

// export interface ShareClass {
//   id: number;
//   client_id: string;
//   stock_authorized: number;
//   stock_issued: number;
//   shares_authorized_preferred: string;
//   shares_issued_preferred: number;
//   notes: string | null;
// }

// export interface Shareholder {
//   id: string;
//   client_id: string;
//   client_person_id: string;
//   position: string;
//   ownership: string;
//   note: string;
// }

// export interface TaxReporting {
//   id?: number;
//   uid?: string;
//   client_id: string;
//   year: string;
//   reporting_1099: string;
//   tax_return_by: string;
//   note: string;
//   files: any | null;
// }

export interface Client extends DataItem{

  internal_draft_flag: boolean;

  id: string;
  // account: string;
  // account_add: string;
  accounting_method: string;
  active_since: string | null;
  agr_signed: string | null;
  agreement_sum: number;
  billing_method: string | null;
  bookkeeping: boolean;
  // changed: string;
  name: string;
  company_phone: string;
  // control_by: string;
  cpa: string;
  // date: string;
  description: string;
  // disable_secreg: boolean;
  dissolution_date: string | null;
  ein: string;
  fedtaxforms: string;
  financial_year_end: string;
  financial_year_end_for_subsidiary: string;
  incorp_by: string;
  legal_ent_type: string;
  login: string;
  manager: string | null;
  monthly_bill: number;
  naicscode: string;
  // none_banks: boolean;
  notes_accounting: string;
  notes_address: string;
  notes_agreement: string;
  notes_contacts: string;
  notes_main: string;
  notes_shareholders: string;
  optional_share_count: number;
  paid_by: string | null;
  paid_by_mail: string | null;
  password: string;
  payroll: boolean;
  renewal_date: string | null;
  renewal_date_mail: string | null;
  since: string;
  source_id: string | null;
  statetaxforms: string;
  status: string;
  subjurisd: string | null;
  subsidiary_legal_entity_type: string | null;
  subsidiary_to_consolidate: string | null;
  total_shares: number;
  withdrawal_date: string | null;
  created_at: string | null;
  updated_at: string | null;
  
  // Related entities
  addresses: ClientAddress[];
  authorized_signers: ClientAuthorizedSigners[];
  bank_accounts: ClientBankAccount[];
  contacts: ClientContact[];
  files: File[];
  payment_cards: ClientPaymentCards[];
  payment_services: ClientPaymentServices[];
  primary_registration: ClientPrimaryRegistration
  secondary_registrations: ClientRegistrationItem[]
  registrations: ClientRegistrationItem[]; // to remove
  services: any[];
  share_classes: ClientShareClasses[];
  shareholders: ClientShareholders[];
  tasks: any[];
  tax_reporting: ClientTaxReporting[];

  internal_data_source: {
    "type": string,
    "id": null | string | number
  }
}


export const clientListSchema: SchemaData = {
  name: {
    type: 'text',
    label: 'Company Name',
    required: true,
    validation: {
      pattern: /^[a-zA-Z0-9\s.,&-]+$/,
      message: 'Company name can contain letters, numbers, spaces, and basic punctuation',
    },
  },
  status: {
    type: 'text',
    label: 'Status',
    // required: true,
    // options: ['hourly', 'active', 'inactive', 'suspended'],
  },
  legal_ent_type: {
    type: 'select',
    label: 'Legal Entity Type',
    required: true,
    options: ['C-CORP', 'S-CORP', 'LLC', 'Partnership', 'Sole Proprietorship'],
  },
  description: {
    type: 'textarea',
    label: 'Description',
    multiline: true,
  },
  internal_draft_flag: {
    type: 'checkbox',
    label: 'draft'
  },
  // tasks: {
  //   type: 'text',
  //   label: 'tasks',
  //   getValue: (value: any, key) => {
  //     if(value.tasks == undefined) return '---'
  //     if(value.tasks.length) console.info('!!!!!TASKS', value.id)
  //     return value.tasks.length
  //   }, 
  // }
}

export const clientSchema: SchemaData = {
  name: {
    type: 'text',
    label: 'Company Name',
    required: true,
    validation: {
      pattern: /^[a-zA-Z0-9\s.,&-]+$/,
      message: 'Company name can contain letters, numbers, spaces, and basic punctuation',
    },
  },
  // account: {
  //   type: 'text',
  //   label: 'Account Number',
  //   required: true,
  // },
  ein: {
    type: 'text',
    label: 'EIN',
    required: true,
    validation: {
      pattern: /^\d{9}$/,
      message: 'EIN must be 9 digits',
    },
  },
  legal_ent_type: {
    type: 'select',
    label: 'Legal Entity Type',
    required: true,
    options: ['C-CORP', 'S-CORP', 'LLC', 'Partnership', 'Sole Proprietorship'],
  },
  status: {
    type: 'select',
    label: 'Status',
    required: true,
    options: ['hourly', 'active', 'inactive', 'suspended'],
  },
  description: {
    type: 'textarea',
    label: 'Description',
    multiline: true,
  },
  company_phone: {
    type: 'text',
    label: 'Company Phone',
    validation: {
      pattern: /^\+?[\d\s-()]+$/,
      message: 'Enter a valid phone number',
    },
  },
  since: {
    type: 'date',
    label: 'Since',
    required: true,
  },
  active_since: {
    type: 'date',
    label: 'Active Since',
  },
  fedtaxforms: {
    type: 'select',
    label: 'Federal Tax Forms',
    required: true,
    options: ['1120', '1120S', '1065', '1040'],
  },
  statetaxforms: {
    type: 'text',
    label: 'State Tax Forms',
    required: true,
  },
  financial_year_end: {
    type: 'select',
    label: 'Fiscal Year End',
    required: true,
    options: ['Dec.', 'Jan.', 'Feb.', 'Mar.', 'Apr.', 'May', 'Jun.', 'Jul.', 'Aug.', 'Sep.', 'Oct.', 'Nov.'],
  },
  accounting_method: {
    type: 'select',
    label: 'Accounting Method',
    required: true,
    options: ['Cash', 'Accrual'],
  },
  bookkeeping: {
    type: 'select',
    label: 'Bookkeeping',
    required: true,
    options: [
      { value: 'true', label: 'Yes' },
      { value: 'false', label: 'No' }
    ],
  },
  payroll: {
    type: 'select',
    label: 'Payroll',
    required: true,
    options: [
      { value: 'true', label: 'Yes' },
      { value: 'false', label: 'No' }
    ],
  },
  naicscode: {
    type: 'text',
    label: 'NAICS Code',
    required: true,
    validation: {
      pattern: /^\d{6}$/,
      message: 'NAICS code must be 6 digits',
    },
  },
  notes_main: {
    type: 'textarea',
    label: 'Main Notes',
    multiline: true,
  },
  notes_accounting: {
    type: 'textarea',
    label: 'Accounting Notes',
    multiline: true,
  },
  notes_address: {
    type: 'textarea',
    label: 'Address Notes',
    multiline: true,
  },
  notes_contacts: {
    type: 'textarea',
    label: 'Contact Notes',
    multiline: true,
  },
  notes_shareholders: {
    type: 'textarea',
    label: 'Shareholder Notes',
    multiline: true,
  },
  total_shares: {
    type: 'number',
    label: 'Total Shares',
    required: true,
    min: 0,
  },
  monthly_bill: {
    type: 'number',
    label: 'Monthly Bill',
    required: true,
    min: 0,
  },
  agreement_sum: {
    type: 'number',
    label: 'Agreement Sum',
    required: true,
    min: 0,
  },
  internal_draft_flag: {
    type: 'boolean',
    label: 'draft'
  }
};
