import React from 'react';
import { Box, Typography, Grid, Alert, Paper } from '@mui/material';
import { SchemaData, SchemaDataValue } from './models/schema';

export interface GridBySchemaProps<T> {
  /** Схема данных для отображения */
  schema: SchemaData;
  /** Данные для отображения */
  data: T[];
  /** Обработчик изменения значений */
  onChange?: (values: T[]) => void;
  /** Заголовок таблицы */
  title?: string;
  /** Ошибка отображения */
  error?: string | null;
  /** Количество колонок для разных размеров экрана */
  columns?: {
    xs?: number;
    sm?: number;
    md?: number;
    lg?: number;
    xl?: number;
  };
}

export const GridBySchema = <T extends Record<string, any>>({
  schema,
  data,
  onChange,
  title,
  error,
  columns = { xs: 12, sm: 6, md: 4, lg: 3, xl: 2 },
}: GridBySchemaProps<T>) => {
  return (
    <Box>
      {title && (
        <Typography variant="h6" sx={{ mb: 2 }}>
          {title}
        </Typography>
      )}
      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}
      <Grid container spacing={2}>
        {data.map((item, index) => (
          <Grid key={index} {...columns}>
            <Paper 
              elevation={1} 
              sx={{ 
                p: 2,
                height: '100%',
                display: 'flex',
                flexDirection: 'column',
                '&:hover': {
                  boxShadow: 3,
                  transition: 'box-shadow 0.2s ease-in-out'
                }
              }}
            >
              {Object.entries(schema).map(([key, field]) => {
                if (field.visible === false) return null;
                
                const value = field.getValue ? field.getValue(item) : item[key];
                const displayValue = formatValue(value, field);

                return (
                  <Box key={key} sx={{ mb: 1 }}>
                    <Typography 
                      variant="subtitle2" 
                      color="text.secondary"
                      sx={{ mb: 0.5 }}
                    >
                      {field.label}
                    </Typography>
                    <Typography 
                      variant="body2"
                      sx={{ 
                        wordBreak: 'break-word',
                        minHeight: '1.5em'
                      }}
                    >
                      {displayValue}
                    </Typography>
                  </Box>
                );
              })}
            </Paper>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

const formatValue = (value: any, field: SchemaDataValue): string => {
  if (value === undefined || value === null) {
    return '-';
  }

  switch (field.type) {
    case 'date':
      return new Date(value).toLocaleDateString();
    case 'textarea':
      return value;
    case 'select':
      if (field.options) {
        const option = field.options.find(opt => 
          typeof opt === 'object' ? opt.value === value : opt === value
        );
        return typeof option === 'object' ? option.label : String(option);
      }
      return String(value);
    case 'autocomplete':
      if (field.autocomplete?.formatOptionLabel && typeof value === 'object') {
        return field.autocomplete.formatOptionLabel(value);
      }
      return String(value);
    default:
      return String(value);
  }
};