import { FormBySchema } from "../FormBySchema";
import { clientMainSchema } from "../models/client/main";
import React from 'react'
import { clientSummaryAdvancedSchema, clientSummarySchema } from "../models/client/summary";
import { Box, Button, Collapse, Paper } from "@mui/material";
import { clientPrimaryRegistrationSchema } from "../models/client/registration_primary";
import { Client } from "../models/client";
  
export interface ClientMainProps<T> {
/** Данные */
data: T;
baseData?: T;
onChange: (data: T) => void;
}


export const ClientMain = <T extends Client >({
    data,
    baseData,
    onChange,
}: ClientMainProps<T>) => {

    const [open, setOpen] = React.useState(false);


    React.useEffect( () => {
        console.info('ClientMain', data)
    }, [])

    return (
    <Box
        sx={{
            'border-bottom' : '1px solid',
            'padding-bottom' : '1em'
        }}
    >
        <FormBySchema
            schema={clientSummarySchema}
            initialValues={data}
            baseValues={baseData}
            onChange={onChange}
            actions={false}
        />

        {/* <Button onClick={() => setOpen(!open)}>Advanced</Button>
        <Collapse in={open} timeout="auto" unmountOnExit>
            <Box sx={{
                'margin-left': '2em',
            }}>
                <Paper
                    sx={{                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       
                        'padding': '1em',
                    }}
                >
                    <FormBySchema
                        schema={clientSummaryAdvancedSchema}
                        initialValues={data}
                        baseValues={baseData}
                        onChange={onChange}
                        actions={false}
                    />
                </Paper>

                <Paper
                    sx={{
                        'padding': '1em',
                    }}
                >
                    <FormBySchema
                        schema={clientPrimaryRegistrationSchema}
                        initialValues={data.primary_registration as any}
                        baseValues={baseData?.primary_registration as any}
                        onChange={onChange}
                        actions={false}
                    />
                </Paper>
            </Box>
        </Collapse>
             */}
        </Box>
    // return (
    //     <div>
    //         <FormBySchema
    //             schema={clientMainSchema}
    //             initialValues={data}
    //             baseValues={baseData}
    //             onChange={onChange}
    //             actions={false}
    //         />
    //     </div>
);
}