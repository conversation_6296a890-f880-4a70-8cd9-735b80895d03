import React from "react";
import { ListFormBySchema } from "../ListFormBySchema";
import { v4 as uuidv4 } from 'uuid';  
import { Client, ClientTabProps } from "../models/client";
import { clientAddressSchema, ClientAddress } from "../models/client/address";
import { FormBySchema } from "../FormBySchema";
import { clientSummaryAdvancedSchema } from "../models/client/summary";
import { ClientRegistrationItem, clientRegistrationItemSchema } from "../models/client/registration_item";
import { clientPrimaryRegistrationSchema } from "../models/client/registration_primary";
import { Box, Tab, Tabs } from "@mui/material";
import { clientContactSchema } from "../models/client/contat";
import { clientShareholdersSchema } from "../models/client/shareholders";
import { clientShareClassesSchema } from "../models/client/share_classes";
import { ClientOwnershipCorp } from "./Ownership/Corp";
import { ClientOwnershipLLC } from "./Ownership/Llc";
  
interface TabPanelProps {
  children?: React.ReactNode;
  dir?: string;
  index: string | number;
  value: string | number;
}


function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`simple-tabpanel-${index}`}
      aria-labelledby={`simple-tab-${index}`}
      {...other}
    >
      <Box sx={{ p: 3 }}>{children}</Box>
      {/* {value === index && <Box sx={{ p: 3 }}>{children}</Box>} */}
    </div>
  );
  }


export const ClientSummaryTab = <T extends Client>({
  data,
    baseData,
    onChange,
  }: ClientTabProps<T>) => {

    const [openTab, setOpenTab] = React.useState<string |number>('registrations');
    const handleChangeTabs = (event: React.SyntheticEvent, newValue: number) => {
      setOpenTab(newValue);
    };
  
    React.useEffect(() => {
      // if (!data.registrations) data.registrations = []
      // if (!data.registrations.length) {
      //   const newData = {...data};
      //   newData.registrations = [{ is_primary: true, uid: uuidv4() }];
      //   onChange(newData);
      // }
    }, []);
  
    React.useEffect(() => {
      console.info('ClientRegistration', data)
    }, []);
  
    const handleListChange = (registrations: ClientRegistrationItem[]) => {
      const newData = {...data, registrations};
      onChange(newData);
    };

    const handleListTabChange = (formData: any[], tab: string) => {
      const newData: any = {...data, [tab]: formData};
      onChange(newData);
    };

    function a11yProps(name: string) {
      return {
        id: `client-tab-${name}`,
        'aria-controls': `clien-tabpanel-${name}`,
        value: name
      };
    }
  
    return (
      <Box>
        <FormBySchema
            schema={clientSummaryAdvancedSchema}
            initialValues={data}
            baseValues={baseData}
            onChange={onChange}
            actions={false}
        />

        <Box sx={{ borderBottom: 1, borderColor: 'divider', marginTop: 3, bgcolor: 'text.secondary', color: 'background.paper', }}>
          <Tabs 
            value={openTab} 
            onChange={handleChangeTabs} 
            aria-label="basic tabs example"
            variant="scrollable"
            scrollButtons="auto"  
            textColor="inherit"
            >
            <Tab key='registration' label='Registration' {...a11yProps('registrations')} ></Tab>
            <Tab key='addresses' label='Addresses' {...a11yProps('addresses')} ></Tab>
            <Tab key='contacts' label='Contacts' {...a11yProps('contacts')} ></Tab>
            <Tab key='ownership' label='OWNERSHIP' {...a11yProps('ownership')} ></Tab>

            <Tab key='shareholders' label='Shareholders' {...a11yProps('shareholders')} ></Tab>
            <Tab key='share_classes' label='Share and stock classes' {...a11yProps('share_classes')} ></Tab>
            
          </Tabs>
        </Box>
        <Box>
          <TabPanel key='registrations' value={openTab} index='registrations' >
            <Box sx={{marginBottom: 3}}>
              <FormBySchema
                  schema={clientPrimaryRegistrationSchema}
                  initialValues={data.primary_registration as any}
                  baseValues={baseData?.primary_registration as any}
                  onChange={(formData) => onChange({...data, primary_registration: formData})}
                  actions={false}
              />
            </Box>
            <ListFormBySchema
              
              schema={clientRegistrationItemSchema}
              baseItems={baseData?.registrations || []}
              items={data.registrations || []}
              onChange={(formData) => handleListTabChange(formData, 'registrations')}
              defaultItemValues={{}}
              addButtonText="Add"
              title="Secondary registrations"
            />   
          </TabPanel>
          <TabPanel key='addresses' value={openTab} index='addresses' >
            <ListFormBySchema
                    schema={clientAddressSchema}
                    items={data.addresses || []}
                    baseItems={baseData?.addresses || []}
                    onChange={(formData) => handleListTabChange(formData, 'addresses')}
                    defaultItemValues={{}}
                    addButtonText="Add"
                    // title="Addresses"
                  />   
          </TabPanel>
          <TabPanel key='contacts' value={openTab} index='contacts' >
            <ListFormBySchema
                    schema={clientContactSchema}
                    items={data['contacts'] || []}
                    baseItems={baseData ? baseData['contacts'] : []}
                    onChange={(formData) => handleListTabChange(formData, 'contacts')}
                    defaultItemValues={{}}
                    addButtonText="Add"
                    // title="Addresses"
                  />   
          </TabPanel>
          <TabPanel key='ownership' value={openTab} index='ownership' >
            { data.legal_ent_type ? 
                data.legal_ent_type.includes('CORP') 
                  ? (<ClientOwnershipCorp></ClientOwnershipCorp>)
                  : (<ClientOwnershipLLC></ClientOwnershipLLC>) 
                : ( <></>)
            }
  
          </TabPanel>

          <TabPanel key='shareholders' value={openTab} index='shareholders' >
            <ListFormBySchema
                    schema={clientShareholdersSchema}
                    items={data['shareholders'] || []}
                    baseItems={baseData ? baseData['shareholders'] : []}
                    onChange={(formData) => handleListTabChange(formData, 'shareholders')}
                    defaultItemValues={{}}
                    addButtonText="Add"
                    // title="Addresses"
                  />   
          </TabPanel>
          <TabPanel key='share_classes' value={openTab} index='share_classes' >
            <ListFormBySchema
                    schema={clientShareClassesSchema}
                    items={data['share_classes'] || []}
                    baseItems={baseData ? baseData['share_classes'] : []}
                    onChange={(formData) => handleListTabChange(formData, 'share_classes')}
                    defaultItemValues={{}}
                    addButtonText="Add"
                    // title="Addresses"
                  />   
          </TabPanel>
        </Box>

        
      </Box>
    );
}